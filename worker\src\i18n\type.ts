export type LocaleMessages = {
    CustomAuthPasswordMsg: string
    UserTokenExpiredMsg: string
    UserAcceesTokenExpiredMsg: string
    UserRoleIsNotAdminMsg: string
    NeedAdminPasswordMsg: string

    KVNotAvailableMsg: string
    DBNotAvailableMsg: string
    JWTSecretNotSetMsg: string
    WebhookNotEnabledMsg: string
    DomainsNotSetMsg: string

    TurnstileCheckFailedMsg: string
    NewAddressDisabledMsg: string
    NewAddressAnonymousDisabledMsg: string
    FailedCreateAddressMsg: string
    InvalidAddressMsg: string
    InvalidAddressCredentialMsg: string
    UserDeleteEmailDisabledMsg: string

    UserNotFoundMsg: string
    UserAlreadyExistsMsg: string
    FailedToRegisterMsg: string
    UserRegistrationDisabledMsg: string
    UserMailDomainMustInMsg: string
    InvalidVerifyCodeMsg: string
    InvalidEmailOrPasswordMsg: string
    VerifyMailSenderNotSetMsg: string
    CodeAlreadySentMsg: string
    InvalidUserDefaultRoleMsg: string
    FailedUpdateUserDefaultRoleMsg: string

    Oauth2ClientIDNotFoundMsg: string
    Oauth2CliendIDOrCodeMissingMsg: string
    Oauth2FailedGetUserInfoMsg: string
    Oauth2FailedGetAccessTokenMsg: string
    Oauth2FailedGetUserEmailMsg: string
}
